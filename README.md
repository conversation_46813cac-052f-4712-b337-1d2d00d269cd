# AI Advertisement Management Platform MVP

A comprehensive web platform that connects AI model providers with advertisers for targeted advertising solutions. Built with Next.js 14+, PostgreSQL, and HeroUI components.

## 🚀 Features

### For AI Model Providers
- **Easy Integration**: Secure API credentials for seamless ad integration
- **Revenue Analytics**: Real-time tracking of impressions, clicks, and earnings
- **App Management**: Register and manage multiple AI applications
- **Competitive Rates**: Flexible CPM and CPC pricing models

### For Advertisers
- **Targeted Campaigns**: Reach specific AI application categories
- **Performance Tracking**: Detailed metrics and ROI analysis
- **Budget Management**: Flexible budget allocation and bidding strategies
- **High-Quality Audience**: Engaged AI application users

### Platform Features
- **Dual Role Support**: Users can be both providers and advertisers
- **Real-time Analytics**: Interactive charts and performance metrics
- **Secure Authentication**: NextAuth.js with role-based access
- **File Upload**: Image upload for advertisements
- **Rate Limiting**: Built-in API protection
- **Responsive Design**: Mobile-first UI with HeroUI components

## 🛠 Tech Stack

- **Frontend**: Next.js 14+ (App Router), React 18, TypeScript
- **UI Components**: HeroUI (NextUI successor)
- **Authentication**: NextAuth.js with credentials provider
- **Database**: PostgreSQL with Prisma ORM
- **Styling**: Tailwind CSS
- **Charts**: Recharts for analytics visualization
- **Validation**: Zod for schema validation
- **Runtime**: Bun (recommended) or Node.js 18+

## 📋 Prerequisites

- Bun runtime or Node.js 18+
- PostgreSQL database
- Git

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/ai-ad-platform.git
cd ai-ad-platform
```

### 2. Install Dependencies
```bash
# Using Bun (recommended)
bun install

# Or using npm
npm install
```

### 3. Environment Setup
Create a `.env.local` file in the root directory:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ai_ad_platform?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 4. Database Setup
```bash
# Generate Prisma client
bunx prisma generate

# Push schema to database
bunx prisma db push
```

### 5. Start Development Server
```bash
# Using Bun
bun run dev

# Or using npm
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📱 Usage

### Getting Started
1. **Register**: Create an account and select your role(s)
2. **Model Providers**: Register your AI apps and get API credentials
3. **Advertisers**: Create campaigns and upload ad creatives
4. **Integration**: Use the API to serve ads in your applications
5. **Analytics**: Monitor performance and optimize campaigns

### API Integration for Model Providers

#### 1. Get Ad for Your Application
```javascript
const response = await fetch('http://localhost:3000/api/serve-ad', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    appId: 'your-app-id',
    appSecret: 'your-app-secret',
    topics: ['AI', 'productivity'], // Optional targeting
    userContext: { // Optional user context
      userAgent: navigator.userAgent,
      language: 'en-US'
    }
  })
})

const { ad, trackingUrl } = await response.json()
```

#### 2. Track Impressions and Clicks
```javascript
// Track impression
await fetch(trackingUrl, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adId: ad.id,
    appId: 'your-app-id',
    clicked: false
  })
})

// Track click
await fetch(trackingUrl, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adId: ad.id,
    appId: 'your-app-id',
    clicked: true
  })
})
```

## 🏗 Architecture

### Database Schema
- **Users**: Authentication and role management
- **Apps**: AI application registration and credentials
- **Advertisements**: Campaign data and targeting
- **AdImpressions**: Tracking data for analytics

### API Endpoints
- `/api/auth/*` - Authentication (NextAuth.js)
- `/api/apps` - App management for providers
- `/api/ads` - Advertisement management
- `/api/serve-ad` - Ad serving for integration
- `/api/impressions` - Impression tracking
- `/api/analytics/*` - Dashboard analytics
- `/api/upload` - File upload for ad images

### Security Features
- Rate limiting on all endpoints
- Input validation with Zod schemas
- CSRF protection
- Secure file upload with type validation
- Role-based access control

## 🚀 Deployment

See [deployment.md](./deployment.md) for detailed deployment instructions including:
- Environment configuration
- Database setup
- Docker deployment
- Vercel deployment
- Security considerations
- Monitoring and logging

## 🧪 Testing

The project uses **Vitest** for fast, modern testing with comprehensive coverage of API routes, utility functions, and integration flows.

### Running Tests

```bash
# Run all tests once
bun run test:run

# Run tests in watch mode (for development)
bun run test:watch

# Run tests with UI (interactive test runner)
bun run test:ui

# Generate coverage report
bun run test:coverage

# Type checking
bun run type-check

# Linting
bun run lint
```

### Test Structure

```
__tests__/
├── api/                          # API route tests
│   ├── auth/
│   │   └── register.test.ts      # User registration tests
│   ├── apps.test.ts              # App management tests
│   ├── ads.test.ts               # Advertisement tests
│   ├── serve-ad.test.ts          # Ad serving tests
│   └── impressions.test.ts       # Impression tracking tests
├── lib/                          # Utility function tests
│   ├── auth.test.ts              # Authentication utilities
│   ├── validation.test.ts        # Input validation tests
│   └── logger.test.ts            # Logging utilities
└── integration/                  # Integration tests
    └── user-flow.test.ts         # End-to-end user flows
```

### Test Coverage

The test suite covers:

- ✅ **API Routes**: All endpoints with success/error scenarios
- ✅ **Authentication**: Login, registration, session management
- ✅ **Validation**: Input validation and sanitization
- ✅ **Database Operations**: Mocked Prisma operations
- ✅ **Security**: Rate limiting, CORS, security headers
- ✅ **Business Logic**: User flows, ad serving, tracking
- ✅ **Error Handling**: Comprehensive error scenarios

### Running Specific Tests

```bash
# Run specific test file
bun run test:run __tests__/api/auth/register.test.ts

# Run tests matching pattern
bun run test:run --grep "registration"

# Run tests for specific directory
bun run test:run __tests__/api/
```

### Test Environment

Tests use:
- **Vitest** for test runner and assertions
- **jsdom** for DOM testing environment
- **MSW** for API mocking (when needed)
- **@testing-library** for React component testing
- **Comprehensive mocks** for external dependencies

### Troubleshooting Tests

If tests fail:

1. **Database Issues**: Tests use mocked Prisma - no real database needed
2. **Import Errors**: Check that all imports are properly mocked in `vitest.setup.ts`
3. **Environment Variables**: Tests use mock values, no real env vars needed
4. **Network Issues**: All external calls are mocked

### Writing New Tests

When adding new features:

1. **API Routes**: Add tests in `__tests__/api/`
2. **Utilities**: Add tests in `__tests__/lib/`
3. **Components**: Add tests alongside components
4. **Integration**: Add flow tests in `__tests__/integration/`

Example test structure:
```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'

describe('Feature Name', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should handle success case', async () => {
    // Arrange
    // Act
    // Assert
  })

  it('should handle error case', async () => {
    // Test error scenarios
  })
})
```

## 📊 Analytics & Monitoring

The platform includes comprehensive analytics:
- **Revenue tracking** for model providers
- **Campaign performance** for advertisers
- **Real-time metrics** with interactive charts
- **Top performers** analysis
- **Click-through rates** and conversion tracking

## 🔒 Security

- **Authentication**: Secure session management
- **Authorization**: Role-based access control
- **Rate Limiting**: API protection against abuse
- **Input Validation**: Comprehensive data validation
- **File Upload**: Secure image handling
- **HTTPS**: SSL/TLS encryption in production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the [deployment guide](./deployment.md)
- Review the API documentation in the code
- Open an issue on GitHub

## 🗺 Roadmap

- [ ] Advanced targeting algorithms
- [ ] Real-time bidding system
- [ ] Machine learning optimization
- [ ] Mobile SDK for easier integration
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Payment processing integration
- [ ] A/B testing framework
