import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { Decimal } from "@prisma/client/runtime/library";
import { BidType, AdStatus } from "@prisma/client";

import { GET, POST } from "@/app/api/ads/route";
import { prisma } from "@/lib/db";

vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/db", () => ({
  prisma: {
    advertisement: {
      findMany: vi.fn(),
      create: vi.fn(),
    },
  },
}));

describe("/api/ads", () => {
  const mockSession = {
    user: {
      id: "user-123",
      email: "<EMAIL>",
      roles: ["ADVERTISER"],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET /api/ads", () => {
    it("should return ads for authenticated advertiser", async () => {
      const mockAds = [
        {
          id: "ad-1",
          userId: "user-123",
          name: "Test Campaign",
          description: "Test description",
          imageUrl: null,
          productUrl: "https://example.com",
          targetTopics: ["AI", "productivity"],
          budget: new Decimal(100),
          bidType: BidType.CPC,
          bidAmount: new Decimal(0.5),
          status: AdStatus.ACTIVE,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.ads).toHaveLength(1);
      expect(data.ads[0]).toMatchObject({
        id: "ad-1",
        name: "Test Campaign",
        budget: 100,
        bidAmount: 0.5,
        impressions: expect.any(Number),
        clicks: expect.any(Number),
        spend: expect.any(Number),
      });
    });

    it("should return 401 for unauthenticated user", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
    });

    it("should return 401 for user without ADVERTISER role", async () => {
      const sessionWithoutRole = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(sessionWithoutRole);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
    });
  });

  describe("POST /api/ads", () => {
    it("should create a new advertisement successfully", async () => {
      const mockAd = {
        id: "ad-1",
        userId: "user-123",
        name: "Test Campaign",
        description: "Test description",
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["AI", "productivity"],
        budget: new Decimal(100),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        status: AdStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(prisma.advertisement.create).mockResolvedValue(mockAd);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        body: JSON.stringify({
          name: "Test Campaign",
          description: "Test description",
          productUrl: "https://example.com",
          targetTopics: ["AI", "productivity"],
          budget: 100,
          bidType: "CPC",
          bidAmount: 0.5,
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.message).toBe("Advertisement created successfully");
      expect(data.ad).toMatchObject({
        id: "ad-1",
        name: "Test Campaign",
        budget: 100,
        bidAmount: 0.5,
        impressions: 0,
        clicks: 0,
        spend: 0,
      });
    });

    it("should return 400 for missing required fields", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        body: JSON.stringify({
          name: "Test Campaign",
          // Missing required fields
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Description is required");
    });

    it("should return 400 for invalid product URL", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        body: JSON.stringify({
          name: "Test Campaign",
          description: "Test description",
          productUrl: "invalid-url",
          targetTopics: ["AI"],
          budget: 100,
          bidType: "CPC",
          bidAmount: 0.5,
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid product URL");
    });

    it("should return 400 for invalid bid type", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        body: JSON.stringify({
          name: "Test Campaign",
          description: "Test description",
          productUrl: "https://example.com",
          targetTopics: ["AI"],
          budget: 100,
          bidType: "INVALID",
          bidAmount: 0.5,
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid enum value");
    });

    it("should return 400 for invalid budget", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        body: JSON.stringify({
          name: "Test Campaign",
          description: "Test description",
          productUrl: "https://example.com",
          targetTopics: ["AI"],
          budget: -10,
          bidType: "CPC",
          bidAmount: 0.5,
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Budget must be at least $1");
    });
  });
});
