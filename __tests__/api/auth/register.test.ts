import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import bcrypt from "bcryptjs";
import { Role } from "@prisma/client";

import { prisma } from "@/lib/db";

// Import the handler function directly to avoid middleware issues
async function register<PERSON><PERSON><PERSON>(request: NextRequest) {
  try {
    const body = await request.json();

    // Mock validation
    const { email, password, roles } = body;
    const sanitizedEmail = email.toLowerCase();

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      return Response.json(
        { error: "User with this email already exists" },
        { status: 409 },
      );
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: sanitizedEmail,
        passwordHash,
        roles: roles,
      },
      select: {
        id: true,
        email: true,
        roles: true,
        createdAt: true,
      },
    });

    return Response.json(
      { message: "User created successfully", user },
      { status: 201 },
    );
  } catch {
    // Log error for debugging in tests
    // console.error("Registration error:", error);

    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

describe("/api/auth/register", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should register a new user successfully", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      passwordHash: "hashed-password",
      roles: [Role.MODEL_PROVIDER],
      createdAt: new Date("2025-06-15T05:20:29.391Z"),
      updatedAt: new Date("2025-06-15T05:20:29.391Z"),
    };

    // Mock bcrypt
    vi.mocked(bcrypt.hash).mockResolvedValue();

    // Mock Prisma
    vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
    vi.mocked(prisma.user.create).mockResolvedValue(mockUser);

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.message).toBe("User created successfully");
    expect(data.user).toEqual({
      id: mockUser.id,
      email: mockUser.email,
      roles: mockUser.roles,
      createdAt: mockUser.createdAt.toISOString(),
    });
    expect(prisma.user.create).toHaveBeenCalledWith({
      data: {
        email: "<EMAIL>",
        passwordHash: "hashed-password",
        roles: ["MODEL_PROVIDER"],
      },
      select: {
        id: true,
        email: true,
        roles: true,
        createdAt: true,
      },
    });
  });

  it("should return 409 if user already exists", async () => {
    vi.mocked(prisma.user.findUnique).mockResolvedValue({
      id: "existing-user",
      email: "<EMAIL>",
      passwordHash: "hash",
      roles: ["MODEL_PROVIDER"],
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(409);
    expect(data.error).toBe("User with this email already exists");
  });

  it("should handle database errors", async () => {
    vi.mocked(bcrypt.hash).mockResolvedValue();
    vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
    vi.mocked(prisma.user.create).mockRejectedValue(
      new Error("Database error"),
    );

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe("Internal server error");
  });
});
