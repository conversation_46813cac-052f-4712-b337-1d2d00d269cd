import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { AdStatus, AppStatus } from "@prisma/client";

vi.mock("@/lib/db", () => ({
  prisma: {
    advertisement: {
      findUnique: vi.fn(),
    },
    app: {
      findUnique: vi.fn(),
    },
    adImpression: {
      create: vi.fn(),
      findMany: vi.fn(),
    },
  },
}));

import { GET, POST } from "@/app/api/impressions/route";
import { prisma } from "@/lib/db";

describe("/api/impressions", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/impressions", () => {
    const mockAd = {
      id: "ad-1",
      status: AdStatus.ACTIVE,
    };

    const mockApp = {
      id: "app-1",
      status: AppStatus.ACTIVE,
    };

    const mockImpression = {
      id: "impression-1",
      adId: "ad-1",
      appId: "app-1",
      clicked: false,
      timestamp: new Date(),
      ipAddress: "127.0.0.1",
      userAgent: "test-agent",
    };

    it("should track impression successfully", async () => {
      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(
        mockAd as any,
      );
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);
      vi.mocked(prisma.adImpression.create).mockResolvedValue(
        mockImpression as any,
      );

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        body: JSON.stringify({
          adId: "ad-1",
          appId: "app-1",
          clicked: false,
          userAgent: "test-agent",
        }),
        headers: {
          "Content-Type": "application/json",
          "x-forwarded-for": "127.0.0.1",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.message).toBe("Impression recorded successfully");
      expect(data.impressionId).toBe("impression-1");
      expect(prisma.adImpression.create).toHaveBeenCalledWith({
        data: {
          adId: "ad-1",
          appId: "app-1",
          clicked: false,
          ipAddress: "127.0.0.1",
          userAgent: "test-agent",
        },
      });
    });

    it("should track click successfully", async () => {
      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(
        mockAd as any,
      );
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({
        ...mockImpression,
        clicked: true,
      } as any);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        body: JSON.stringify({
          adId: "ad-1",
          appId: "app-1",
          clicked: true,
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.message).toBe("Impression recorded successfully");
      expect(prisma.adImpression.create).toHaveBeenCalledWith({
        data: {
          adId: "ad-1",
          appId: "app-1",
          clicked: true,
          ipAddress: "unknown",
          userAgent: null,
        },
      });
    });

    it("should return 400 for missing required fields", async () => {
      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        body: JSON.stringify({
          adId: "ad-1",
          // Missing appId
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Required");
    });

    it("should return 404 for non-existent advertisement", async () => {
      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(null);
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        body: JSON.stringify({
          adId: "non-existent-ad",
          appId: "app-1",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("Advertisement not found or inactive");
    });

    it("should return 404 for inactive advertisement", async () => {
      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue({
        ...mockAd,
        status: AdStatus.PAUSED,
      } as any);
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        body: JSON.stringify({
          adId: "ad-1",
          appId: "app-1",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("Advertisement not found or inactive");
    });

    it("should return 404 for non-existent app", async () => {
      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(
        mockAd as any,
      );
      vi.mocked(prisma.app.findUnique).mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        body: JSON.stringify({
          adId: "ad-1",
          appId: "non-existent-app",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("App not found or inactive");
    });
  });

  describe("GET /api/impressions", () => {
    const mockImpressions = [
      {
        id: "impression-1",
        timestamp: new Date(),
        clicked: false,
        ipAddress: "127.0.0.1",
        advertisement: {
          id: "ad-1",
          name: "Test Ad",
        },
        app: {
          id: "app-1",
          name: "Test App",
        },
      },
      {
        id: "impression-2",
        timestamp: new Date(),
        clicked: true,
        ipAddress: "127.0.0.1",
        advertisement: {
          id: "ad-1",
          name: "Test Ad",
        },
        app: {
          id: "app-1",
          name: "Test App",
        },
      },
    ];

    it("should return impressions for app", async () => {
      (prisma.adImpression.findMany as any).mockResolvedValue(mockImpressions);

      const request = new NextRequest(
        "http://localhost:3000/api/impressions?appId=app-1",
        {
          method: "GET",
        },
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.impressions).toHaveLength(2);
      expect(data.stats).toEqual({
        totalImpressions: 2,
        totalClicks: 1,
        clickThroughRate: "50.00",
      });
    });

    it("should return impressions for ad", async () => {
      (prisma.adImpression.findMany as any).mockResolvedValue(mockImpressions);

      const request = new NextRequest(
        "http://localhost:3000/api/impressions?adId=ad-1",
        {
          method: "GET",
        },
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.impressions).toHaveLength(2);
      expect(data.stats.totalImpressions).toBe(2);
      expect(data.stats.totalClicks).toBe(1);
    });

    it("should return 400 when neither appId nor adId provided", async () => {
      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Either appId or adId is required");
    });

    it("should handle date range filtering", async () => {
      (prisma.adImpression.findMany as any).mockResolvedValue([]);

      const request = new NextRequest(
        "http://localhost:3000/api/impressions?appId=app-1&startDate=2024-01-01&endDate=2024-12-31",
        {
          method: "GET",
        },
      );

      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(prisma.adImpression.findMany).toHaveBeenCalledWith({
        where: {
          appId: "app-1",
          timestamp: {
            gte: new Date("2024-01-01"),
            lte: new Date("2024-12-31"),
          },
        },
        select: expect.any(Object),
        orderBy: { timestamp: "desc" },
        take: 1000,
      });
    });
  });
});
