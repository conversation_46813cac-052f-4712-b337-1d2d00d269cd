import { describe, it, expect, vi, beforeEach } from "vitest";
import bcrypt from "bcryptjs";
import { Role } from "@prisma/client";

vi.mock("bcryptjs", () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  },
}));

vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
    },
  },
}));

import { hashPassword, verifyPassword, authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";

describe("auth utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("hashPassword", () => {
    it("should hash password with bcrypt", async () => {
      const password = "testpassword123";
      const hashedPassword = "hashed-password";

      vi.mocked(bcrypt.hash).mockResolvedValue(hashedPassword as any);

      const result = await hashPassword(password);

      expect(bcrypt.hash).toHaveBeenCalledWith(password, 12);
      expect(result).toBe(hashedPassword);
    });

    it("should handle bcrypt errors", async () => {
      const password = "testpassword123";
      const error = new Error("Bcrypt error");

      vi.mocked(bcrypt.hash).mockRejectedValue(error);

      await expect(hashPassword(password)).rejects.toThrow("Bcrypt error");
    });
  });

  describe("verifyPassword", () => {
    it("should verify correct password", async () => {
      const password = "testpassword123";
      const hashedPassword = "hashed-password";

      vi.mocked(bcrypt.compare).mockResolvedValue(true as any);

      const result = await verifyPassword(password, hashedPassword);

      expect(bcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
      expect(result).toBe(true);
    });

    it("should reject incorrect password", async () => {
      const password = "wrongpassword";
      const hashedPassword = "hashed-password";

      vi.mocked(bcrypt.compare).mockResolvedValue(false as any);

      const result = await verifyPassword(password, hashedPassword);

      expect(bcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
      expect(result).toBe(false);
    });

    it("should handle bcrypt errors", async () => {
      const password = "testpassword123";
      const hashedPassword = "hashed-password";
      const error = new Error("Bcrypt error");

      vi.mocked(bcrypt.compare).mockRejectedValue(error);

      await expect(verifyPassword(password, hashedPassword)).rejects.toThrow(
        "Bcrypt error",
      );
    });
  });

  describe("authOptions", () => {
    it("should have correct configuration", () => {
      expect(authOptions.providers).toHaveLength(1);
      expect(authOptions.session?.strategy).toBe("jwt");
      expect(authOptions.session?.maxAge).toBe(7 * 24 * 60 * 60); // 7 days
      expect(authOptions.pages?.signIn).toBe("/login");
    });

    describe("credentials provider", () => {
      it("should authorize valid user", async () => {
        const mockUser = {
          id: "user-123",
          email: "<EMAIL>",
          passwordHash: "hashed-password",
          roles: [Role.MODEL_PROVIDER],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
        vi.mocked(bcrypt.compare).mockResolvedValue(true as any);

        const provider = authOptions.providers[0];

        if ("authorize" in provider && provider.authorize) {
          const result = await provider.authorize(
            {
              email: "<EMAIL>",
              password: "password123",
            },
            {} as any,
          );

          expect(result).toEqual({
            id: "user-123",
            email: "<EMAIL>",
            roles: ["MODEL_PROVIDER"],
          });
        }
      });

      it("should reject user with wrong password", async () => {
        const mockUser = {
          id: "user-123",
          email: "<EMAIL>",
          passwordHash: "hashed-password",
          roles: [Role.MODEL_PROVIDER],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
        vi.mocked(bcrypt.compare).mockResolvedValue(false as any);

        const provider = authOptions.providers[0];

        if ("authorize" in provider && provider.authorize) {
          const result = await provider.authorize(
            {
              email: "<EMAIL>",
              password: "wrongpassword",
            },
            {} as any,
          );

          expect(result).toBeNull();
        }
      });

      it("should reject non-existent user", async () => {
        vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

        const provider = authOptions.providers[0];

        if ("authorize" in provider && provider.authorize) {
          const result = await provider.authorize(
            {
              email: "<EMAIL>",
              password: "password123",
            },
            {} as any,
          );

          expect(result).toBeNull();
        }
      });

      it("should reject missing credentials", async () => {
        const provider = authOptions.providers[0];

        if ("authorize" in provider && provider.authorize) {
          const result1 = await provider.authorize(
            {
              email: "<EMAIL>",
              // Missing password
            },
            {} as any,
          );

          const result2 = await provider.authorize(
            {
              password: "password123",
              // Missing email
            },
            {} as any,
          );

          expect(result1).toBeNull();
          expect(result2).toBeNull();
        }
      });
    });

    describe("JWT callback", () => {
      it("should add roles to token", async () => {
        const token = { sub: "user-123" };
        const user = { roles: ["MODEL_PROVIDER"] };

        const result = await authOptions.callbacks?.jwt?.({
          token,
          user,
        } as any);

        expect(result).toEqual({
          sub: "user-123",
          roles: ["MODEL_PROVIDER"],
        });
      });

      it("should preserve existing token when no user", async () => {
        const token = { sub: "user-123", roles: ["ADVERTISER"] };

        const result = await authOptions.callbacks?.jwt?.({ token } as any);

        expect(result).toEqual(token);
      });
    });

    describe("session callback", () => {
      it("should add user info to session", async () => {
        const session = {
          user: { email: "<EMAIL>" },
        };
        const token = {
          sub: "user-123",
          roles: ["MODEL_PROVIDER"],
        };

        const result = await authOptions.callbacks?.session?.({
          session,
          token,
        } as any);

        expect(result).toEqual({
          user: {
            email: "<EMAIL>",
            id: "user-123",
            roles: ["MODEL_PROVIDER"],
          },
        });
      });

      it("should handle missing token", async () => {
        const session = {
          user: { email: "<EMAIL>" },
        };

        const result = await authOptions.callbacks?.session?.({
          session,
        } as any);

        expect(result).toEqual(session);
      });
    });
  });
});
