import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import {
  logger,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  RateLimitError,
  handleApiError,
} from "@/lib/logger";

describe("logger utilities", () => {
  let consoleSpy: any;

  beforeEach(() => {
    vi.clearAllMocks();
    // Set NODE_ENV for tests using vi.stubEnv
    vi.stubEnv("NODE_ENV", "development");
    consoleSpy = {
      error: vi.spyOn(console, "error").mockImplementation(() => {}),
      warn: vi.spyOn(console, "warn").mockImplementation(() => {}),
      info: vi.spyOn(console, "info").mockImplementation(() => {}),
      debug: vi.spyOn(console, "debug").mockImplementation(() => {}),
      log: vi.spyOn(console, "log").mockImplementation(() => {}),
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.unstubAllEnvs();
  });

  describe("logger methods", () => {
    it("should log error messages", () => {
      const message = "Test error message";
      const context = { userId: "user-123" };
      const error = new Error("Test error");

      logger.error(message, context, error);

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining(message),
      );
    });

    it("should log warning messages", () => {
      const message = "Test warning message";
      const context = { action: "test" };

      logger.warn(message, context);

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining(message),
      );
    });

    it("should log info messages", () => {
      const message = "Test info message";
      const context = { operation: "test" };

      logger.info(message, context);

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining(message),
      );
    });

    it("should log debug messages in development", () => {
      // Mock development environment
      const originalTestEnv = process.env.NODE_ENV;

      Object.defineProperty(process.env, "NODE_ENV", {
        value: "development",
        writable: true,
        configurable: true,
      });

      const message = "Test debug message";
      const context = { debug: true };

      logger.debug(message, context);

      expect(consoleSpy.debug).toHaveBeenCalledWith(
        expect.stringContaining(message),
      );

      // Restore original environment
      if (originalTestEnv !== undefined) {
        Object.defineProperty(process.env, "NODE_ENV", {
          value: originalTestEnv,
          writable: true,
          configurable: true,
        });
      }
    });
  });

  describe("API-specific logging", () => {
    it("should log API requests", () => {
      logger.apiRequest("POST", "/api/test", "user-123", { body: "test" });

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining("API Request: POST /api/test"),
      );
    });

    it("should log API responses", () => {
      logger.apiResponse("POST", "/api/test", 200, 150, "user-123");

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining("API Response: POST /api/test"),
      );
    });

    it("should log API errors", () => {
      const error = new Error("API error");

      logger.apiError("POST", "/api/test", error, "user-123");

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining("API Error: POST /api/test"),
      );
    });
  });

  describe("security logging", () => {
    it("should log security events", () => {
      logger.securityEvent("Suspicious activity", { ip: "127.0.0.1" });

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining("Security Event: Suspicious activity"),
      );
    });

    it("should log rate limit exceeded", () => {
      logger.rateLimitExceeded("user-123", "/api/test");

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining("Rate limit exceeded"),
      );
    });

    it("should log authentication failures", () => {
      logger.authFailure("<EMAIL>", "Invalid password", "127.0.0.1");

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining("Authentication failure"),
      );
    });
  });

  describe("business logic logging", () => {
    it("should log user registration", () => {
      logger.userRegistered("user-123", "<EMAIL>", ["MODEL_PROVIDER"]);

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining("User registered"),
      );
    });

    it("should log app registration", () => {
      logger.appRegistered("app-123", "user-123", "Test App");

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining("App registered"),
      );
    });

    it("should log ad creation", () => {
      logger.adCreated("ad-123", "user-123", "Test Campaign", 100);

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining("Advertisement created"),
      );
    });

    it("should log ad serving", () => {
      logger.adServed("ad-123", "app-123", ["AI", "productivity"]);

      expect(consoleSpy.debug).toHaveBeenCalledWith(
        expect.stringContaining("Ad served"),
      );
    });

    it("should log impression tracking", () => {
      logger.impressionTracked("ad-123", "app-123", true);

      expect(consoleSpy.debug).toHaveBeenCalledWith(
        expect.stringContaining("Impression tracked"),
      );
    });
  });

  describe("error classes", () => {
    it("should create AppError with correct properties", () => {
      const error = new AppError("Test error", 400, "TEST_ERROR", {
        test: true,
      });

      expect(error.message).toBe("Test error");
      expect(error.statusCode).toBe(400);
      expect(error.code).toBe("TEST_ERROR");
      expect(error.context).toEqual({ test: true });
      expect(error.name).toBe("AppError");
    });

    it("should create ValidationError", () => {
      const error = new ValidationError("Validation failed", {
        field: "email",
      });

      expect(error.message).toBe("Validation failed");
      expect(error.statusCode).toBe(400);
      expect(error.code).toBe("VALIDATION_ERROR");
      expect(error.context).toEqual({ field: "email" });
      expect(error.name).toBe("ValidationError");
    });

    it("should create AuthenticationError", () => {
      const error = new AuthenticationError();

      expect(error.message).toBe("Authentication required");
      expect(error.statusCode).toBe(401);
      expect(error.code).toBe("AUTHENTICATION_ERROR");
      expect(error.name).toBe("AuthenticationError");
    });

    it("should create AuthorizationError", () => {
      const error = new AuthorizationError();

      expect(error.message).toBe("Insufficient permissions");
      expect(error.statusCode).toBe(403);
      expect(error.code).toBe("AUTHORIZATION_ERROR");
      expect(error.name).toBe("AuthorizationError");
    });

    it("should create NotFoundError", () => {
      const error = new NotFoundError();

      expect(error.message).toBe("Resource not found");
      expect(error.statusCode).toBe(404);
      expect(error.code).toBe("NOT_FOUND_ERROR");
      expect(error.name).toBe("NotFoundError");
    });

    it("should create RateLimitError", () => {
      const error = new RateLimitError("Rate limit exceeded", 60);

      expect(error.message).toBe("Rate limit exceeded");
      expect(error.statusCode).toBe(429);
      expect(error.code).toBe("RATE_LIMIT_ERROR");
      expect(error.context).toEqual({ retryAfter: 60 });
      expect(error.name).toBe("RateLimitError");
    });
  });

  describe("handleApiError", () => {
    it("should handle AppError correctly", () => {
      const error = new ValidationError("Invalid input", { field: "email" });
      const result = handleApiError(error);

      expect(result.status).toBe(400);
      expect(result.body.error).toBe("Invalid input");
      expect(result.body.code).toBe("VALIDATION_ERROR");
    });

    it("should handle generic Error", () => {
      const error = new Error("Generic error");
      const result = handleApiError(error);

      expect(result.status).toBe(500);
      expect(result.body.error).toBe("Internal server error");
    });

    it("should handle unknown error types", () => {
      const error = "String error";
      const result = handleApiError(error);

      expect(result.status).toBe(500);
      expect(result.body.error).toBe("Internal server error");
    });

    it("should include context in development", () => {
      const originalEnv = process.env.NODE_ENV;

      Object.defineProperty(process.env, "NODE_ENV", {
        value: "development",
        writable: true,
        configurable: true,
      });

      const error = new ValidationError("Invalid input", { field: "email" });
      const result = handleApiError(error);

      expect(result.body.context).toEqual({ field: "email" });

      // Restore original environment
      if (originalEnv !== undefined) {
        Object.defineProperty(process.env, "NODE_ENV", {
          value: originalEnv,
          writable: true,
          configurable: true,
        });
      }
    });

    it("should not include context in production", () => {
      vi.stubEnv("NODE_ENV", "production");

      const error = new ValidationError("Invalid input", { field: "email" });
      const result = handleApiError(error);

      expect(result.body.context).toBeUndefined();

      vi.unstubAllEnvs();
    });
  });
});
