import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";

export async function GET(
  _request: NextRequest,
  { params }: { params: { role: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { role } = await params;

    if (!["model", "advertiser"].includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 });
    }

    const requiredRole = role === "model" ? "MODEL_PROVIDER" : "ADVERTISER";

    if (!session.user.roles.includes(requiredRole as any)) {
      return NextResponse.json(
        { error: "Unauthorized for this role" },
        { status: 403 },
      );
    }

    if (role === "model") {
      // Model Provider Analytics
      const apps = await prisma.app.findMany({
        where: { userId: session.user.id },
        select: { id: true, name: true, createdAt: true },
      });

      // Mock analytics data for now
      const analytics = {
        totalApps: apps.length,
        totalImpressions: Math.floor(Math.random() * 50000),
        totalClicks: Math.floor(Math.random() * 2500),
        totalRevenue: Math.random() * 500,
        monthlyData: Array.from({ length: 12 }, (_, i) => ({
          month: new Date(2024, i).toLocaleString("default", {
            month: "short",
          }),
          impressions: Math.floor(Math.random() * 5000),
          clicks: Math.floor(Math.random() * 250),
          revenue: Math.random() * 50,
        })),
        topApps: apps.slice(0, 5).map((app) => ({
          ...app,
          impressions: Math.floor(Math.random() * 10000),
          clicks: Math.floor(Math.random() * 500),
          revenue: Math.random() * 100,
        })),
      };

      return NextResponse.json({ analytics });
    } else {
      // Advertiser Analytics
      const ads = await prisma.advertisement.findMany({
        where: { userId: session.user.id },
        select: {
          id: true,
          name: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          createdAt: true,
        },
      });

      const totalBudget = ads.reduce((sum, ad) => sum + Number(ad.budget), 0);

      // Mock analytics data for now
      const analytics = {
        totalCampaigns: ads.length,
        totalBudget,
        totalSpend: Math.random() * totalBudget * 0.8,
        totalImpressions: Math.floor(Math.random() * 30000),
        totalClicks: Math.floor(Math.random() * 1500),
        averageCTR: (Math.random() * 5).toFixed(2),
        monthlyData: Array.from({ length: 12 }, (_, i) => ({
          month: new Date(2024, i).toLocaleString("default", {
            month: "short",
          }),
          impressions: Math.floor(Math.random() * 3000),
          clicks: Math.floor(Math.random() * 150),
          spend: Math.random() * (totalBudget / 12),
        })),
        topCampaigns: ads.slice(0, 5).map((ad) => ({
          ...ad,
          budget: Number(ad.budget),
          bidAmount: Number(ad.bidAmount),
          impressions: Math.floor(Math.random() * 5000),
          clicks: Math.floor(Math.random() * 250),
          spend: Math.random() * Number(ad.budget) * 0.8,
          ctr: (Math.random() * 5).toFixed(2),
        })),
      };

      return NextResponse.json({ analytics });
    }
  } catch {
    // Log error for debugging
    // console.error("Analytics fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
