import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { nanoid } from "nanoid";

import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { appSchema, validateData } from "@/lib/validation";

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const apps = await prisma.app.findMany({
      where: {
        userId: session.user.id,
      },
      select: {
        id: true,
        name: true,
        appId: true,
        status: true,
        createdAt: true,
        description: true,
        // Add aggregated impression data later
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // For now, return mock analytics data
    const appsWithAnalytics = apps.map((app) => ({
      ...app,
      impressions: Math.floor(Math.random() * 10000),
      clicks: Math.floor(Math.random() * 500),
      revenue: Math.random() * 100,
    }));

    return NextResponse.json({ apps: appsWithAnalytics });
  } catch {
    // Log error for debugging
    // console.error("Apps fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(appSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { name, description } = validation.data;

    // Generate unique app ID and secret
    const appId = `app_${nanoid(16)}`;
    const appSecret = `secret_${nanoid(32)}`;

    const app = await prisma.app.create({
      data: {
        userId: session.user.id,
        name,
        appId,
        appSecret,
        description: description || null,
      },
      select: {
        id: true,
        name: true,
        appId: true,
        status: true,
        createdAt: true,
        description: true,
      },
    });

    return NextResponse.json(
      {
        message: "App created successfully",
        app: {
          ...app,
          impressions: 0,
          clicks: 0,
          revenue: 0,
        },
      },
      { status: 201 },
    );
  } catch {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
