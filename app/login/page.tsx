"use client";

import { useState } from "react";
import { signIn, getSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Input } from "@heroui/input";
import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";
import { Checkbox } from "@heroui/checkbox";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError("Invalid email or password");
      } else {
        // Get session to determine redirect
        const session = await getSession();

        if (session?.user?.roles) {
          // Redirect based on user roles
          if (session.user.roles.includes("MODEL_PROVIDER")) {
            router.push("/dashboard/model");
          } else if (session.user.roles.includes("ADVERTISER")) {
            router.push("/dashboard/advertiser");
          } else {
            router.push("/dashboard");
          }
        }
      }
    } catch {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <h1 className="text-2xl font-bold">Sign In</h1>
          <p className="text-default-600">Welcome back to AI Ad Platform</p>
        </CardHeader>
        <CardBody>
          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 text-sm rounded-lg text-danger bg-danger/10">
                {error}
              </div>
            )}

            <Input
              required
              isDisabled={isLoading}
              label="Email"
              placeholder="Enter your email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />

            <Input
              required
              isDisabled={isLoading}
              label="Password"
              placeholder="Enter your password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />

            <div className="flex items-center justify-between">
              <Checkbox
                isSelected={rememberMe}
                size="sm"
                onValueChange={setRememberMe}
              >
                Remember me
              </Checkbox>
              <Link href="/forgot-password" size="sm">
                Forgot password?
              </Link>
            </div>

            <Button
              className="w-full"
              color="primary"
              isLoading={isLoading}
              type="submit"
            >
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>

            <div className="text-sm text-center text-default-600">
              Don&apos;t have an account?{" "}
              <Link color="primary" href="/register">
                Sign up
              </Link>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}
