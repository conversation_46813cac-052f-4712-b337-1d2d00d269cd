import { Link } from "@heroui/link";
import { But<PERSON> } from "@heroui/button";
import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";

import { title, subtitle } from "@/components/primitives";

export default function Home() {
  return (
    <div className="flex flex-col gap-16 py-8">
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-6 text-center">
        <div className="inline-block max-w-4xl">
          <h1 className={title({ size: "lg" })}>
            Connect AI Models with&nbsp;
            <span className={title({ color: "violet", size: "lg" })}>
              Targeted Advertising
            </span>
          </h1>
          <p className={subtitle({ class: "mt-6 text-lg" })}>
            The premier platform for AI model providers to monetize their
            applications and advertisers to reach engaged AI users with
            precision targeting.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <Button
            as={Link}
            className="font-semibold"
            color="primary"
            href="/register"
            size="lg"
          >
            Get Started Free
          </Button>
          <Button
            as={Link}
            className="font-semibold"
            href="/login"
            size="lg"
            variant="bordered"
          >
            Sign In
          </Button>
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-6xl mx-auto" id="features">
        <div className="text-center mb-12">
          <h2 className={title({ size: "md" })}>
            Powerful Features for Both Sides
          </h2>
          <p className={subtitle({ class: "mt-4" })}>
            Everything you need to succeed in AI advertising
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Model Providers */}
          <Card className="p-6">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <Chip color="primary" variant="flat">
                  For Providers
                </Chip>
                <h3 className="text-xl font-bold">Monetize Your AI Apps</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <ul className="space-y-3 text-default-600">
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  Easy integration with secure API credentials
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  Real-time revenue analytics and reporting
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  Competitive CPM and CPC rates
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  Automated ad serving and optimization
                </li>
              </ul>
            </CardBody>
          </Card>

          {/* Advertisers */}
          <Card className="p-6">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <Chip color="secondary" variant="flat">
                  For Advertisers
                </Chip>
                <h3 className="text-xl font-bold">Reach AI Users</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <ul className="space-y-3 text-default-600">
                <li className="flex items-start gap-2">
                  <span className="text-secondary">✓</span>
                  Target specific AI application categories
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-secondary">✓</span>
                  Detailed performance metrics and ROI tracking
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-secondary">✓</span>
                  Flexible budget management and bidding
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-secondary">✓</span>
                  High-quality, engaged AI user audience
                </li>
              </ul>
            </CardBody>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="text-center bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-12">
        <h2 className={title({ size: "md" })}>Ready to Get Started?</h2>
        <p className={subtitle({ class: "mt-4 mb-8" })}>
          Join thousands of AI providers and advertisers already using our
          platform
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            as={Link}
            className="font-semibold"
            color="primary"
            href="/register"
            size="lg"
          >
            Start as Provider
          </Button>
          <Button
            as={Link}
            className="font-semibold"
            color="secondary"
            href="/register"
            size="lg"
          >
            Start as Advertiser
          </Button>
        </div>
      </section>
    </div>
  );
}
