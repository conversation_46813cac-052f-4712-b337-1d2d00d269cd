"use client";

import {
  Navbar as Hero<PERSON><PERSON><PERSON><PERSON>,
  NavbarContent,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@heroui/navbar";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@heroui/dropdown";
import { Avatar } from "@heroui/avatar";
import { link as linkStyles } from "@heroui/theme";
import NextLink from "next/link";
import clsx from "clsx";
import { useSession, signOut } from "next-auth/react";

import { siteConfig } from "@/config/site";
import { ThemeSwitch } from "@/components/theme-switch";
import { Logo } from "@/components/icons";

export const Navbar = () => {
  const { data: session, status } = useSession();

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" });
  };

  return (
    <HeroUINavbar maxWidth="xl" position="sticky">
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink className="flex items-center justify-start gap-1" href="/">
            <Logo />
            <p className="font-bold text-inherit">AI Ad Platform</p>
          </NextLink>
        </NavbarBrand>
        <ul className="justify-start hidden gap-4 ml-2 lg:flex">
          {siteConfig.navItems.map((item) => (
            <NavbarItem key={item.href}>
              <NextLink
                className={clsx(
                  linkStyles({ color: "foreground" }),
                  "data-[active=true]:text-primary data-[active=true]:font-medium",
                )}
                color="foreground"
                href={item.href}
              >
                {item.label}
              </NextLink>
            </NavbarItem>
          ))}
        </ul>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <NavbarItem className="hidden gap-2 sm:flex">
          <ThemeSwitch />
        </NavbarItem>

        {status === "loading" ? (
          <NavbarItem>
            <div className="w-8 h-8 rounded-full animate-pulse bg-default-200" />
          </NavbarItem>
        ) : session ? (
          <NavbarItem>
            <Dropdown placement="bottom-end">
              <DropdownTrigger>
                <Avatar
                  as="button"
                  className="transition-transform"
                  color="primary"
                  name={session.user.email?.charAt(0).toUpperCase()}
                  size="sm"
                />
              </DropdownTrigger>
              <DropdownMenu aria-label="Profile Actions" variant="flat">
                <DropdownItem key="profile" className="gap-2 h-14">
                  <p className="font-semibold">Signed in as</p>
                  <p className="font-semibold">{session.user.email}</p>
                </DropdownItem>
                {session.user.roles.includes("MODEL_PROVIDER") && (
                  <DropdownItem
                    key="model-dashboard"
                    as={NextLink}
                    href="/dashboard/model"
                  >
                    Model Dashboard
                  </DropdownItem>
                )}
                {session.user.roles.includes("ADVERTISER") && (
                  <DropdownItem
                    key="advertiser-dashboard"
                    as={NextLink}
                    href="/dashboard/advertiser"
                  >
                    Advertiser Dashboard
                  </DropdownItem>
                )}
                <DropdownItem key="settings" as={NextLink} href="/settings">
                  Settings
                </DropdownItem>
                <DropdownItem
                  key="logout"
                  color="danger"
                  onClick={handleSignOut}
                >
                  Log Out
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </NavbarItem>
        ) : (
          <NavbarItem className="flex gap-2">
            <Button as={NextLink} href="/login" size="sm" variant="flat">
              Sign In
            </Button>
            <Button as={NextLink} color="primary" href="/register" size="sm">
              Sign Up
            </Button>
          </NavbarItem>
        )}
      </NavbarContent>

      <NavbarContent className="pl-4 sm:hidden basis-1" justify="end">
        <ThemeSwitch />
        <NavbarMenuToggle />
      </NavbarContent>

      <NavbarMenu>
        <div className="flex flex-col gap-2 mx-4 mt-2">
          {session ? (
            <>
              <NavbarMenuItem>
                <p className="text-sm text-default-600">
                  Signed in as {session.user.email}
                </p>
              </NavbarMenuItem>
              {session.user.roles.includes("MODEL_PROVIDER") && (
                <NavbarMenuItem>
                  <Link as={NextLink} href="/dashboard/model" size="lg">
                    Model Dashboard
                  </Link>
                </NavbarMenuItem>
              )}
              {session.user.roles.includes("ADVERTISER") && (
                <NavbarMenuItem>
                  <Link as={NextLink} href="/dashboard/advertiser" size="lg">
                    Advertiser Dashboard
                  </Link>
                </NavbarMenuItem>
              )}
              <NavbarMenuItem>
                <Link as={NextLink} href="/settings" size="lg">
                  Settings
                </Link>
              </NavbarMenuItem>
              <NavbarMenuItem>
                <Link color="danger" size="lg" onClick={handleSignOut}>
                  Log Out
                </Link>
              </NavbarMenuItem>
            </>
          ) : (
            <>
              <NavbarMenuItem>
                <Link as={NextLink} href="/login" size="lg">
                  Sign In
                </Link>
              </NavbarMenuItem>
              <NavbarMenuItem>
                <Link as={NextLink} color="primary" href="/register" size="lg">
                  Sign Up
                </Link>
              </NavbarMenuItem>
            </>
          )}
        </div>
      </NavbarMenu>
    </HeroUINavbar>
  );
};
