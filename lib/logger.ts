// Simple logging utility for the AI Ad Platform

export enum LogLevel {
  ERROR = "ERROR",
  WARN = "WARN",
  INFO = "INFO",
  DEBUG = "DEBUG",
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
}

class Logger {
  private get isDevelopment() {
    return process.env.NODE_ENV === "development";
  }

  private formatLog(entry: LogEntry): string {
    const { timestamp, level, message, context, error } = entry;
    let logString = `[${timestamp}] ${level}: ${message}`;

    if (context && Object.keys(context).length > 0) {
      logString += ` | Context: ${JSON.stringify(context)}`;
    }

    if (error) {
      logString += ` | Error: ${error.message}`;
      if (this.isDevelopment && error.stack) {
        logString += `\nStack: ${error.stack}`;
      }
    }

    return logString;
  }

  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error,
  ) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error,
    };

    const formattedLog = this.formatLog(entry);

    // In development, use console methods for better formatting
    if (this.isDevelopment) {
      switch (level) {
        case LogLevel.ERROR:
          // eslint-disable-next-line no-console
          console.error(formattedLog);
          break;
        case LogLevel.WARN:
          // eslint-disable-next-line no-console
          console.warn(formattedLog);
          break;
        case LogLevel.INFO:
          // eslint-disable-next-line no-console
          console.info(formattedLog);
          break;
        case LogLevel.DEBUG:
          // eslint-disable-next-line no-console
          console.debug(formattedLog);
          break;
      }
    } else {
      // In production, use structured logging (could be sent to external service)
      // eslint-disable-next-line no-console
      console.log(JSON.stringify(entry));
    }
  }

  error(message: string, context?: Record<string, any>, error?: Error) {
    this.log(LogLevel.ERROR, message, context, error);
  }

  warn(message: string, context?: Record<string, any>) {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: Record<string, any>) {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: Record<string, any>) {
    if (this.isDevelopment) {
      this.log(LogLevel.DEBUG, message, context);
    }
  }

  // API-specific logging methods
  apiRequest(
    method: string,
    path: string,
    userId?: string,
    context?: Record<string, any>,
  ) {
    this.info(`API Request: ${method} ${path}`, {
      method,
      path,
      userId,
      ...context,
    });
  }

  apiResponse(
    method: string,
    path: string,
    status: number,
    duration: number,
    userId?: string,
  ) {
    this.info(`API Response: ${method} ${path}`, {
      method,
      path,
      status,
      duration,
      userId,
    });
  }

  apiError(
    method: string,
    path: string,
    error: Error,
    userId?: string,
    context?: Record<string, any>,
  ) {
    this.error(
      `API Error: ${method} ${path}`,
      {
        method,
        path,
        userId,
        ...context,
      },
      error,
    );
  }

  // Security-related logging
  securityEvent(event: string, context?: Record<string, any>) {
    this.warn(`Security Event: ${event}`, context);
  }

  rateLimitExceeded(identifier: string, endpoint: string) {
    this.warn("Rate limit exceeded", {
      identifier,
      endpoint,
      timestamp: new Date().toISOString(),
    });
  }

  authFailure(email: string, reason: string, ip?: string) {
    this.warn("Authentication failure", {
      email,
      reason,
      ip,
      timestamp: new Date().toISOString(),
    });
  }

  // Business logic logging
  userRegistered(userId: string, email: string, roles: string[]) {
    this.info("User registered", {
      userId,
      email,
      roles,
    });
  }

  appRegistered(appId: string, userId: string, appName: string) {
    this.info("App registered", {
      appId,
      userId,
      appName,
    });
  }

  adCreated(adId: string, userId: string, adName: string, budget: number) {
    this.info("Advertisement created", {
      adId,
      userId,
      adName,
      budget,
    });
  }

  adServed(adId: string, appId: string, topics?: string[]) {
    this.debug("Ad served", {
      adId,
      appId,
      topics,
    });
  }

  impressionTracked(adId: string, appId: string, clicked: boolean) {
    this.debug("Impression tracked", {
      adId,
      appId,
      clicked,
    });
  }
}

// Export singleton instance
export const logger = new Logger();

// Error handling utilities
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public context?: Record<string, any>,
  ) {
    super(message);
    this.name = "AppError";
  }
}

export class ValidationError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 400, "VALIDATION_ERROR", context);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends AppError {
  constructor(
    message: string = "Authentication required",
    context?: Record<string, any>,
  ) {
    super(message, 401, "AUTHENTICATION_ERROR", context);
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends AppError {
  constructor(
    message: string = "Insufficient permissions",
    context?: Record<string, any>,
  ) {
    super(message, 403, "AUTHORIZATION_ERROR", context);
    this.name = "AuthorizationError";
  }
}

export class NotFoundError extends AppError {
  constructor(
    message: string = "Resource not found",
    context?: Record<string, any>,
  ) {
    super(message, 404, "NOT_FOUND_ERROR", context);
    this.name = "NotFoundError";
  }
}

export class RateLimitError extends AppError {
  constructor(
    message: string = "Rate limit exceeded",
    retryAfter: number,
    context?: Record<string, any>,
  ) {
    super(message, 429, "RATE_LIMIT_ERROR", { ...context, retryAfter });
    this.name = "RateLimitError";
  }
}

// Error handler for API routes
export function handleApiError(error: unknown): { status: number; body: any } {
  if (error instanceof AppError) {
    logger.error(`API Error: ${error.name}`, error.context, error);

    return {
      status: error.statusCode,
      body: {
        error: error.message,
        code: error.code,
        ...(process.env.NODE_ENV === "development" && {
          context: error.context,
        }),
      },
    };
  }

  if (error instanceof Error) {
    logger.error("Unexpected API Error", {}, error);

    return {
      status: 500,
      body: {
        error: "Internal server error",
        ...(process.env.NODE_ENV === "development" && {
          message: error.message,
        }),
      },
    };
  }

  logger.error("Unknown API Error", { error });

  return {
    status: 500,
    body: { error: "Internal server error" },
  };
}
