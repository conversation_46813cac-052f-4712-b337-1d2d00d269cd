import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "./auth";
import {
  checkRateLimit,
  rateLimitConfigs,
  RateLimitConfig,
} from "./validation";

// Rate limiting middleware
export function withRateLimit(config: RateLimitConfig) {
  return function (
    handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  ) {
    return async function (
      req: NextRequest,
      ...args: any[]
    ): Promise<NextResponse> {
      // Get client identifier (IP + User-Agent)
      const forwarded = req.headers.get("x-forwarded-for");
      const ip = forwarded
        ? forwarded.split(",")[0]
        : req.headers.get("x-real-ip") || "unknown";
      const userAgent = req.headers.get("user-agent") || "unknown";
      const identifier = `${ip}:${userAgent.slice(0, 50)}`;

      const { allowed, remaining, resetTime } = checkRateLimit(
        identifier,
        config,
      );

      if (!allowed) {
        return NextResponse.json(
          {
            error: "Rate limit exceeded",
            retryAfter: Math.ceil((resetTime - Date.now()) / 1000),
          },
          {
            status: 429,
            headers: {
              "X-RateLimit-Limit": config.maxRequests.toString(),
              "X-RateLimit-Remaining": "0",
              "X-RateLimit-Reset": Math.ceil(resetTime / 1000).toString(),
              "Retry-After": Math.ceil(
                (resetTime - Date.now()) / 1000,
              ).toString(),
            },
          },
        );
      }

      const response = await handler(req, ...args);

      // Add rate limit headers to successful responses
      response.headers.set("X-RateLimit-Limit", config.maxRequests.toString());
      response.headers.set("X-RateLimit-Remaining", remaining.toString());
      response.headers.set(
        "X-RateLimit-Reset",
        Math.ceil(resetTime / 1000).toString(),
      );

      return response;
    };
  };
}

// Authentication middleware
export function withAuth(requiredRoles?: string[]) {
  return function (
    handler: (
      req: NextRequest,
      session: any,
      ...args: any[]
    ) => Promise<NextResponse>,
  ) {
    return async function (
      req: NextRequest,
      ...args: any[]
    ): Promise<NextResponse> {
      const session = await getServerSession(authOptions);

      if (!session) {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 },
        );
      }

      if (requiredRoles && requiredRoles.length > 0) {
        const hasRequiredRole = requiredRoles.some((role) =>
          session.user.roles.includes(role as any),
        );

        if (!hasRequiredRole) {
          return NextResponse.json(
            { error: "Insufficient permissions" },
            { status: 403 },
          );
        }
      }

      return handler(req, session, ...args);
    };
  };
}

// CORS middleware
export function withCors(allowedOrigins: string[] = ["http://localhost:3000"]) {
  return function (
    handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  ) {
    return async function (
      req: NextRequest,
      ...args: any[]
    ): Promise<NextResponse> {
      const origin = req.headers.get("origin");

      // Handle preflight requests
      if (req.method === "OPTIONS") {
        const response = new NextResponse(null, { status: 200 });

        if (origin && allowedOrigins.includes(origin)) {
          response.headers.set("Access-Control-Allow-Origin", origin);
        }

        response.headers.set(
          "Access-Control-Allow-Methods",
          "GET, POST, PUT, DELETE, OPTIONS",
        );
        response.headers.set(
          "Access-Control-Allow-Headers",
          "Content-Type, Authorization",
        );
        response.headers.set("Access-Control-Max-Age", "86400");

        return response;
      }

      const response = await handler(req, ...args);

      // Add CORS headers to actual requests
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set("Access-Control-Allow-Origin", origin);
      }

      response.headers.set(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS",
      );
      response.headers.set(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization",
      );

      return response;
    };
  };
}

// Security headers middleware
export function withSecurityHeaders() {
  return function (
    handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  ) {
    return async function (
      req: NextRequest,
      ...args: any[]
    ): Promise<NextResponse> {
      const response = await handler(req, ...args);

      // Add security headers
      response.headers.set("X-Content-Type-Options", "nosniff");
      response.headers.set("X-Frame-Options", "DENY");
      response.headers.set("X-XSS-Protection", "1; mode=block");
      response.headers.set(
        "Referrer-Policy",
        "strict-origin-when-cross-origin",
      );
      response.headers.set(
        "Content-Security-Policy",
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;",
      );

      return response;
    };
  };
}

// Combine multiple middlewares
export function withMiddleware(...middlewares: Array<(handler: any) => any>) {
  return function (handler: any) {
    return middlewares.reduceRight(
      (acc, middleware) => middleware(acc),
      handler,
    );
  };
}

// Common middleware combinations
export const withApiSecurity = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.api),
);

export const withAuthAndSecurity = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.api),
  withAuth(),
);

export const withUploadSecurity = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.upload),
  withAuth(),
);

export const withServeSecurity = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.serve),
  withCors(["*"]), // Allow all origins for ad serving
);
